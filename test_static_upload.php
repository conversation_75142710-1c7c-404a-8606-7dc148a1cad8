<?php
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h2>Static Upload Test Results</h2>";
    
    echo "<h3>POST Data:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    echo "<h3>FILES Data:</h3>";
    echo "<pre>" . print_r($_FILES, true) . "</pre>";
    
    if (isset($_FILES['imagenes_activos'])) {
        echo "<h3>Processing Files:</h3>";
        foreach ($_FILES['imagenes_activos']['tmp_name'] as $asset_id => $tmp_name) {
            if (!empty($tmp_name) && $_FILES['imagenes_activos']['error'][$asset_id] === UPLOAD_ERR_OK) {
                echo "<p><strong>Asset $asset_id:</strong> File uploaded successfully!</p>";
                echo "Name: " . $_FILES['imagenes_activos']['name'][$asset_id] . "<br>";
                echo "Size: " . $_FILES['imagenes_activos']['size'][$asset_id] . " bytes<br>";
                echo "Type: " . $_FILES['imagenes_activos']['type'][$asset_id] . "<br>";
                
                // Try to move the file
                $upload_dir = __DIR__ . '/resources/uploads/inventario_movimientos/test/';
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                $filename = date('YmdHis') . '-' . $asset_id . '.jpg';
                $full_path = $upload_dir . $filename;
                
                if (move_uploaded_file($tmp_name, $full_path)) {
                    echo "<p style='color: green;'>SUCCESS: File moved to $full_path</p>";
                } else {
                    echo "<p style='color: red;'>ERROR: Failed to move file</p>";
                }
                echo "<hr>";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Static Upload Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Static Upload Test</h1>
        
        <form method="POST" enctype="multipart/form-data">
            <div class="mb-3">
                <label>Select Assets:</label><br>
                <input type="checkbox" name="activos[]" value="10" id="asset-10">
                <label for="asset-10">Asset 10</label><br>
                <input type="checkbox" name="activos[]" value="15" id="asset-15">
                <label for="asset-15">Asset 15</label><br>
            </div>
            
            <div class="mb-3">
                <h4>Upload Images:</h4>
                <div class="mb-2">
                    <label for="imagen_activo_10">Image for Asset 10:</label>
                    <input type="file" name="imagenes_activos[10]" id="imagen_activo_10" class="form-control">
                </div>
                <div class="mb-2">
                    <label for="imagen_activo_15">Image for Asset 15:</label>
                    <input type="file" name="imagenes_activos[15]" id="imagen_activo_15" class="form-control">
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary">Test Upload</button>
        </form>
    </div>
</body>
</html>
