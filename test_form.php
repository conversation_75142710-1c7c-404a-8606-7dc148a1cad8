<?php
require_once __DIR__ . '/config/config.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h2>Form Submission Test</h2>";
    
    echo "<h3>POST Data:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    echo "<h3>FILES Data:</h3>";
    echo "<pre>" . print_r($_FILES, true) . "</pre>";
    
    // Test the exact same structure as the main form
    if (isset($_FILES['imagenes_activos'])) {
        echo "<h3>Processing imagenes_activos:</h3>";
        foreach ($_FILES['imagenes_activos']['tmp_name'] as $asset_id => $tmp_name) {
            if (!empty($tmp_name)) {
                echo "Asset ID: $asset_id<br>";
                echo "Temp file: $tmp_name<br>";
                echo "Original name: " . $_FILES['imagenes_activos']['name'][$asset_id] . "<br>";
                echo "File type: " . $_FILES['imagenes_activos']['type'][$asset_id] . "<br>";
                echo "File size: " . $_FILES['imagenes_activos']['size'][$asset_id] . "<br>";
                echo "Error code: " . $_FILES['imagenes_activos']['error'][$asset_id] . "<br>";
                
                // Try to move the file
                $upload_dir = __DIR__ . '/resources/uploads/inventario_movimientos/test/';
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                $filename = date('YmdHis') . '-' . $asset_id . '.jpg';
                $full_path = $upload_dir . $filename;
                
                if (move_uploaded_file($tmp_name, $full_path)) {
                    echo "<p style='color: green;'>SUCCESS: File moved to $full_path</p>";
                } else {
                    echo "<p style='color: red;'>ERROR: Failed to move file</p>";
                }
                echo "<hr>";
            }
        }
    } else {
        echo "<p style='color: red;'>No imagenes_activos found in FILES</p>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Form Test</title>
</head>
<body>
    <h1>Test Form - Exact Structure</h1>
    
    <form method="POST" enctype="multipart/form-data">
        <h3>Select Assets:</h3>
        <input type="checkbox" name="activos[]" value="1" id="activo-1">
        <label for="activo-1">Asset 1</label><br>
        
        <input type="checkbox" name="activos[]" value="2" id="activo-2">
        <label for="activo-2">Asset 2</label><br>
        
        <h3>Upload Images:</h3>
        <label>Image for Asset 1:</label>
        <input type="file" name="imagenes_activos[1]" accept="image/*"><br><br>
        
        <label>Image for Asset 2:</label>
        <input type="file" name="imagenes_activos[2]" accept="image/*"><br><br>
        
        <button type="submit">Submit Test</button>
    </form>
</body>
</html>
