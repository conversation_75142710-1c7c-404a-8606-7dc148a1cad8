document.addEventListener('DOMContentLoaded', function () {
    // Form elements
    const form = document.getElementById('prestar-form');
    const contenedorSelect = document.getElementById('id_contenedor');
    const trabajadorSelect = document.getElementById('id_trabajador');
    const activosFeedback = document.getElementById('activos-feedback');
    const activosValidation = document.getElementById('activos-validation');
    const activosCard = document.getElementById('activos-card');
    const selectAllCheckbox = document.getElementById('select-all');
    const activosContainer = document.getElementById('activos-container');
    const activosLoading = document.getElementById('activos-loading');
    const activosEmpty = document.getElementById('activos-empty');
    const activosSelectContainer = document.getElementById('activos-select-container');
    const searchInput = document.getElementById('search-activos');
    const btnPrestar = document.getElementById('btn-prestar');
    
    // Function to validate activos selection
    function validateActivosSelection() {
        // If "select all" is checked, we know we have items selected
        if (selectAllCheckbox && selectAllCheckbox.checked) {
            activosFeedback.style.display = 'none';
            activosValidation.setCustomValidity('');
            return true;
        }
        
        const activosChecked = document.querySelectorAll('.activo-checkbox:checked');
        if (activosChecked.length === 0) {
            activosFeedback.style.display = 'block';
            activosValidation.setCustomValidity('Please select at least one activo');
            return false;
        } else {
            activosFeedback.style.display = 'none';
            activosValidation.setCustomValidity('');
            return true;
        }
    }
    
    // Function to load activos for a contenedor
    function loadActivos(contenedorId) {
        if (!contenedorId) {
            activosContainer.innerHTML = '';
            activosEmpty.classList.add('d-none');
            activosSelectContainer.classList.add('d-none');
            return;
        }
        
        // Show loading indicator
        activosLoading.style.display = 'block';
        activosEmpty.classList.add('d-none');
        activosSelectContainer.classList.add('d-none');
        
        // Fetch activos via AJAX
        fetch(`prestar_activos?ajax=get_activos&id_contenedor=${contenedorId}`)
            .then(response => response.json())
            .then(data => {
                // Hide loading indicator
                activosLoading.style.display = 'none';
                
                if (data.error) {
                    console.error('Error loading activos:', data.error);
                    activosEmpty.textContent = 'Error al cargar activos: ' + data.error;
                    activosEmpty.classList.remove('d-none');
                    return;
                }
                
                if (!data.length) {
                    activosEmpty.classList.remove('d-none');
                    return;
                }
                
                // Show activos container
                activosSelectContainer.classList.remove('d-none');
                
                // Build activos checkboxes
                let html = '';
                data.forEach(activo => {
                    let descripcion = activo.activo_descripcion;
                    let detalles = '';
                    
                    if (activo.marca || activo.modelo) {
                        detalles = `<small class="d-block text-muted">${(activo.marca || '') + ' ' + (activo.modelo || '')}</small>`;
                    }
                    
                    html += `
                    <div class="col-md-4 mb-2 activo-item">
                        <div class="form-check">
                            <input class="form-check-input activo-checkbox" type="checkbox" name="activos[]" value="${activo.id_activo}" id="activo-${activo.id_activo}">
                            <label class="form-check-label" for="activo-${activo.id_activo}">
                                ${descripcion}
                                ${detalles}
                            </label>
                        </div>
                    </div>`;
                });
                
                activosContainer.innerHTML = html;
                
                // Add event listeners to new checkboxes
                document.querySelectorAll('.activo-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', validateActivosSelection);
                });
                
                // Reset select all checkbox
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }
                
                // Update validation state
                validateActivosSelection();
            })
            .catch(error => {
                console.error('Error:', error);
                activosLoading.style.display = 'none';
                activosEmpty.textContent = 'Error al cargar activos. Por favor, inténtelo de nuevo.';
                activosEmpty.classList.remove('d-none');
            });
    }
    
    // Event listener for contenedor select change
    if (contenedorSelect) {
        contenedorSelect.addEventListener('change', function() {
            const contenedorId = this.value;
            loadActivos(contenedorId);
        });
        
        // Load activos for initial contenedor if selected
        if (contenedorSelect.value) {
            loadActivos(contenedorSelect.value);
        }
    }
    
    // Add event listeners to checkboxes to update validation state
    document.querySelectorAll('.activo-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', validateActivosSelection);
    });
    
    // Update validation when "select all" is clicked
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.activo-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
            
            // Hide error message immediately when "select all" is checked
            if (selectAllCheckbox.checked) {
                activosFeedback.style.display = 'none';
                activosValidation.setCustomValidity('');
            } else {
                // Only validate if unchecking "select all"
                validateActivosSelection();
            }
        });
    }
    
    // Form submission validation
    if (form) {
        form.addEventListener('submit', function(event) {
            // Validate activos selection
            const activosValid = validateActivosSelection();
            
            // Check form validity
            if (!form.checkValidity() || !activosValid) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            // Add validation classes
            form.classList.add('was-validated');
        });
    }
    
    // Initialize select2 for better dropdown experience (if available)
    if (typeof $.fn.select2 !== 'undefined') {
        $('#id_contenedor, #id_trabajador').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });
    }
    
    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = searchInput.value.toLowerCase();
            const activoItems = document.querySelectorAll('.activo-item');
            
            activoItems.forEach(item => {
                const label = item.querySelector('.form-check-label').textContent.toLowerCase();
                if (label.includes(searchTerm)) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
});
