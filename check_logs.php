<?php
echo "<h2>PHP Error Log Information</h2>";
echo "Error log location: " . ini_get('error_log') . "<br>";
echo "Log errors enabled: " . (ini_get('log_errors') ? 'Yes' : 'No') . "<br>";
echo "Display errors: " . ini_get('display_errors') . "<br>";

// Try to read the last few lines of the error log
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    echo "<h3>Last 20 lines of error log:</h3>";
    $lines = file($error_log);
    $last_lines = array_slice($lines, -20);
    echo "<pre>" . htmlspecialchars(implode('', $last_lines)) . "</pre>";
} else {
    echo "<p>Error log file not found or not accessible.</p>";
    
    // Try common locations
    $common_locations = [
        __DIR__ . '/../../logs/php_error.log',
        __DIR__ . '/../logs/php_error.log',
        '/xampp/logs/php_error.log',
        'C:/xampp/logs/php_error.log'
    ];
    
    echo "<h3>Checking common log locations:</h3>";
    foreach ($common_locations as $location) {
        if (file_exists($location)) {
            echo "<p style='color: green;'>Found: $location</p>";
            $lines = file($location);
            $last_lines = array_slice($lines, -10);
            echo "<pre>" . htmlspecialchars(implode('', $last_lines)) . "</pre>";
            break;
        } else {
            echo "<p style='color: red;'>Not found: $location</p>";
        }
    }
}
?>
