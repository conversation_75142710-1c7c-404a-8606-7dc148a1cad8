<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class InventarioMovimiento
{
	// --- Atributos ---
	private ?int $id           = null;
	private ?string $fecha     = null;
	private ?string $tipo_movimiento = null;
	private ?int $id_contenedor = null;
	private ?int $id_activo    = null;
	private ?int $cantidad     = null;
	private ?int $id_usuario   = null;
	private ?int $id_trabajador = null;
	private ?string $imagen    = null;
	private ?string $contenedor_descripcion = null;
	private ?string $activo_descripcion = null;
	private ?string $usuario_nombre = null;
	private ?string $trabajador_nombre = null;
	
	/**
	 * Constructor: Inicializa las propiedades del objeto InventarioMovimiento.
	 */
	public function __construct()
	{
		$this->id                    = 0; // O null si prefieres no usar 0 por defecto
		$this->fecha                 = null;
		$this->tipo_movimiento       = null;
		$this->id_contenedor         = null;
		$this->id_activo             = null;
		$this->cantidad              = null;
		$this->id_usuario            = null;
		$this->id_trabajador         = null;
		$this->imagen                = null;
		$this->contenedor_descripcion = null;
		$this->activo_descripcion    = null;
		$this->usuario_nombre        = null;
		$this->trabajador_nombre     = null;
	}
	
	/**
	 * Método estático para construir un objeto InventarioMovimiento desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del movimiento.
	 *
	 * @return self Instancia de InventarioMovimiento.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                         = new self();
			$objeto->id                     = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->fecha                  = $resultado['fecha'] ?? null;
			$objeto->tipo_movimiento        = $resultado['tipo_movimiento'] ?? null;
			$objeto->id_contenedor          = isset($resultado['id_contenedor']) ? (int)$resultado['id_contenedor'] : null;
			$objeto->id_activo              = isset($resultado['id_activo']) ? (int)$resultado['id_activo'] : null;
			$objeto->cantidad               = isset($resultado['cantidad']) ? (int)$resultado['cantidad'] : null;
			$objeto->id_usuario             = isset($resultado['id_usuario']) ? (int)$resultado['id_usuario'] : null;
			$objeto->id_trabajador          = isset($resultado['id_trabajador']) ? (int)$resultado['id_trabajador'] : null;
			$objeto->imagen                 = $resultado['imagen'] ?? null;
			$objeto->contenedor_descripcion = $resultado['contenedor_descripcion'] ?? null;
			$objeto->activo_descripcion     = $resultado['activo_descripcion'] ?? null;
			$objeto->usuario_nombre         = $resultado['usuario_nombre'] ?? null;
			$objeto->trabajador_nombre      = $resultado['trabajador_nombre'] ?? null;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir InventarioMovimiento: " . $e->getMessage());
		}
	}
	
	// --- Métodos de Acceso a Datos (Estáticos) ---
	
	/**
	 * Obtiene un movimiento de inventario por su ID.
	 *
	 * @param int $id       ID del movimiento.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto InventarioMovimiento o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener movimiento por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM inventario_movimientos
            WHERE
            	id = :id
            LIMIT 1
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			return $resultado ? self::construct($resultado) : null;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener InventarioMovimiento (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de movimientos de inventario con opción de filtrado.
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @param array $parametros Array asociativo con parámetros de filtrado (opcional).
	 *                         Posibles claves: fecha_inicio, fecha_fin, tipo_movimiento,
	 *                         id_contenedor, id_activo, id_usuario, id_trabajador.
	 *
	 * @return array Array de objetos InventarioMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion, array $parametros = []): array
	{
		try {
			// Consulta base con joins a tablas relacionadas
			$query = <<<SQL
            SELECT
                im.id,
                im.fecha,
                im.tipo_movimiento,
                im.cantidad,
                im.id_contenedor,
                c.descripcion AS contenedor_descripcion,
                im.id_activo,
                a.descripcion AS activo_descripcion,
                im.id_usuario,
                u.nombre AS usuario_nombre,
                im.id_trabajador,
                CONCAT(t.apellidos, ' ', t.nombre) AS trabajador_nombre,
                im.imagen
            FROM
                inventario_movimientos im
            LEFT JOIN
                contenedores c ON im.id_contenedor = c.id
            LEFT JOIN
                activos a ON im.id_activo = a.id
            LEFT JOIN
                usuarios u ON im.id_usuario = u.id
            LEFT JOIN
                trabajadores t ON im.id_trabajador = t.id
            SQL;
			
			// Construir cláusula WHERE basada en parámetros
			$whereConditions = [];
			$bindParams = [];
			
			// Filtro por rango de fechas
			if (!empty($parametros['fecha_inicio']) && !empty($parametros['fecha_fin'])) {
				$whereConditions[] = "im.fecha BETWEEN :fecha_inicio AND :fecha_fin";
				$bindParams[':fecha_inicio'] = $parametros['fecha_inicio'];
				$bindParams[':fecha_fin'] = $parametros['fecha_fin'];
			}
			
			// Filtro por tipo de movimiento
			if (!empty($parametros['tipo_movimiento'])) {
				$whereConditions[] = "im.tipo_movimiento = :tipo_movimiento";
				$bindParams[':tipo_movimiento'] = $parametros['tipo_movimiento'];
			}
			
			// Filtro por contenedor
			if (!empty($parametros['id_contenedor'])) {
				$whereConditions[] = "im.id_contenedor = :id_contenedor";
				$bindParams[':id_contenedor'] = $parametros['id_contenedor'];
			}
			
			// Filtro por activo
			if (!empty($parametros['id_activo'])) {
				$whereConditions[] = "im.id_activo = :id_activo";
				$bindParams[':id_activo'] = $parametros['id_activo'];
			}
			
			// Filtro por usuario
			if (!empty($parametros['id_usuario'])) {
				$whereConditions[] = "im.id_usuario = :id_usuario";
				$bindParams[':id_usuario'] = $parametros['id_usuario'];
			}

			// Filtro por trabajador
			if (!empty($parametros['id_trabajador'])) {
				$whereConditions[] = "im.id_trabajador = :id_trabajador";
				$bindParams[':id_trabajador'] = (int)$parametros['id_trabajador'];
			}
			
			// Agregar cláusula WHERE si hay condiciones
			if (!empty($whereConditions)) {
				$query .= " WHERE " . implode(" AND ", $whereConditions);
			}
			
			// Agregar ordenamiento
			$query .= " ORDER BY im.fecha DESC";
			
			$statement = $conexion->prepare($query);
			
			// Bind de parámetros
			foreach ($bindParams as $param => $value) {
				$type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
				$statement->bindValue($param, $value, $type);
			}
			
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de InventarioMovimientos: " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene movimientos de inventario por contenedor.
	 *
	 * @param int $id_contenedor ID del contenedor.
	 * @param PDO $conexion      Conexión PDO.
	 *
	 * @return array Array de objetos InventarioMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_contenedor(int $id_contenedor, PDO $conexion): array
	{
		try {
			// Consulta para obtener movimientos por contenedor (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM inventario_movimientos
            WHERE
            	id_contenedor = :id_contenedor
            ORDER BY
            	fecha DESC
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_contenedor", $id_contenedor, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener movimientos por contenedor (ID: $id_contenedor): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene movimientos de inventario por activo.
	 *
	 * @param int $id_activo ID del activo.
	 * @param PDO $conexion  Conexión PDO.
	 *
	 * @return array Array de objetos InventarioMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_activo(int $id_activo, PDO $conexion): array
	{
		try {
			// Consulta para obtener movimientos por activo (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM inventario_movimientos
            WHERE
            	id_activo = :id_activo
            ORDER BY
            	fecha DESC
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_activo", $id_activo, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener movimientos por activo (ID: $id_activo): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene movimientos de inventario por usuario.
	 *
	 * @param int $id_usuario ID del usuario.
	 * @param PDO $conexion   Conexión PDO.
	 *
	 * @return array Array de objetos InventarioMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_usuario(int $id_usuario, PDO $conexion): array
	{
		try {
			// Consulta para obtener movimientos por usuario (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM inventario_movimientos
            WHERE
            	id_usuario = :id_usuario
            ORDER BY
            	fecha DESC
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_usuario", $id_usuario, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener movimientos por usuario (ID: $id_usuario): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene movimientos de inventario por trabajador.
	 *
	 * @param int $id_trabajador ID del trabajador.
	 * @param PDO $conexion      Conexión PDO.
	 *
	 * @return array Array de objetos InventarioMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_trabajador(int $id_trabajador, PDO $conexion): array
	{
		try {
			// Consulta para obtener movimientos por trabajador (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM inventario_movimientos
            WHERE
            	id_trabajador = :id_trabajador
            ORDER BY
            	fecha DESC
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_trabajador", $id_trabajador, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener movimientos por trabajador (ID: $id_trabajador): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene movimientos de inventario por rango de fechas.
	 *
	 * @param string $fecha_inicio Fecha de inicio (formato Y-m-d H:i:s).
	 * @param string $fecha_fin    Fecha de fin (formato Y-m-d H:i:s).
	 * @param PDO    $conexion     Conexión PDO.
	 *
	 * @return array Array de objetos InventarioMovimiento.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_fecha_range(string $fecha_inicio, string $fecha_fin, PDO $conexion): array
	{
		try {
			// Consulta para obtener movimientos por rango de fechas (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM inventario_movimientos
            WHERE
            	fecha BETWEEN :fecha_inicio AND :fecha_fin
            ORDER BY
            	fecha DESC
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":fecha_inicio", $fecha_inicio, PDO::PARAM_STR);
			$statement->bindValue(":fecha_fin", $fecha_fin, PDO::PARAM_STR);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener movimientos por rango de fechas: " . $e->getMessage());
		}
	}
	
	/**
	 * Crea un nuevo movimiento de inventario en la base de datos a partir de un objeto InventarioMovimiento.
	 * El objeto InventarioMovimiento debe estar completamente poblado con los datos requeridos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo movimiento creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if (empty($this->getId_contenedor()) || empty($this->getId_activo()) || empty($this->getCantidad()) || empty($this->getId_usuario()) || empty($this->getTipo_movimiento())) {
			throw new Exception("Contenedor, activo, cantidad, tipo de movimiento y usuario son requeridos en el objeto InventarioMovimiento para crearlo.");
		}
		
		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO inventario_movimientos (
            	 id_contenedor
            	,id_activo
            	,cantidad
            	,id_usuario
            	,id_trabajador
            	,tipo_movimiento
            	,imagen
            ) VALUES (
            	 :id_contenedor
            	,:id_activo
            	,:cantidad
            	,:id_usuario
            	,:id_trabajador
            	,:tipo_movimiento
            	,:imagen
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':id_contenedor', $this->getId_contenedor(), PDO::PARAM_INT);
			$statement->bindValue(':id_activo', $this->getId_activo(), PDO::PARAM_INT);
			$statement->bindValue(':cantidad', $this->getCantidad(), PDO::PARAM_INT);
			$statement->bindValue(':id_usuario', $this->getId_usuario(), PDO::PARAM_INT);
			$statement->bindValue(':id_trabajador', $this->getId_trabajador(), PDO::PARAM_INT);
			$statement->bindValue(':tipo_movimiento', $this->getTipo_movimiento(), PDO::PARAM_STR);
			$statement->bindValue(':imagen', $this->getImagen(), PDO::PARAM_STR);
			
			// Ejecutar la consulta
			$success = $statement->execute();
			
			if ($success) {
				// Devolver el ID del movimiento recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}
			
		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear movimiento de inventario: " . $e->getMessage());
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear movimiento de inventario: " . $e->getMessage());
		}
	}
	
	// --- Getters y Setters ---
	
	public function getId(): ?int
	{
		return $this->id;
	}
	
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}
	
	public function getFecha(): ?string
	{
		return $this->fecha;
	}
	
	public function setFecha(?string $fecha): self
	{
		$this->fecha = $fecha;
		return $this;
	}
	
	public function getId_contenedor(): ?int
	{
		return $this->id_contenedor;
	}
	
	public function setId_contenedor(?int $id_contenedor): self
	{
		$this->id_contenedor = $id_contenedor;
		return $this;
	}
	
	public function getId_activo(): ?int
	{
		return $this->id_activo;
	}
	
	public function setId_activo(?int $id_activo): self
	{
		$this->id_activo = $id_activo;
		return $this;
	}
	
	public function getCantidad(): ?int
	{
		return $this->cantidad;
	}
	
	public function setCantidad(?int $cantidad): self
	{
		$this->cantidad = $cantidad;
		return $this;
	}
	
	public function getId_usuario(): ?int
	{
		return $this->id_usuario;
	}
	
	public function setId_usuario(?int $id_usuario): self
	{
		$this->id_usuario = $id_usuario;
		return $this;
	}
	
	public function getTipo_movimiento(): ?string
	{
		return $this->tipo_movimiento;
	}
	
	public function setTipo_movimiento(?string $tipo_movimiento): self
	{
		$this->tipo_movimiento = $tipo_movimiento;
		return $this;
	}
	
	public function getContenedor_descripcion(): ?string
	{
		return $this->contenedor_descripcion;
	}
	
	public function setContenedor_descripcion(?string $contenedor_descripcion): self
	{
		$this->contenedor_descripcion = $contenedor_descripcion;
		return $this;
	}
	
	public function getActivo_descripcion(): ?string
	{
		return $this->activo_descripcion;
	}
	
	public function setActivo_descripcion(?string $activo_descripcion): self
	{
		$this->activo_descripcion = $activo_descripcion;
		return $this;
	}
	
	public function getUsuario_nombre(): ?string
	{
		return $this->usuario_nombre;
	}
	
	public function setUsuario_nombre(?string $usuario_nombre): self
	{
		$this->usuario_nombre = $usuario_nombre;
		return $this;
	}

	public function getId_trabajador(): ?int
	{
		return $this->id_trabajador;
	}

	public function setId_trabajador(?int $id_trabajador): self
	{
		$this->id_trabajador = $id_trabajador;
		return $this;
	}

	public function getTrabajador_nombre(): ?string
	{
		return $this->trabajador_nombre;
	}

	public function setTrabajador_nombre(?string $trabajador_nombre): self
	{
		$this->trabajador_nombre = $trabajador_nombre;
		return $this;
	}

	public function getImagen(): ?string
	{
		return $this->imagen;
	}

	public function setImagen(?string $imagen): self
	{
		$this->imagen = $imagen;
		return $this;
	}

	// --- Métodos adicionales ---
	
	/**
	 * Obtiene el saldo actual de un activo en un contenedor específico.
	 *
	 * @param int $id_contenedor ID del contenedor.
	 * @param int $id_activo     ID del activo.
	 * @param PDO $conexion      Conexión PDO.
	 *
	 * @return int Saldo actual del activo en el contenedor.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_saldo_actual(int $id_contenedor, int $id_activo, PDO $conexion): int
	{
		try {
			// Consulta para obtener el saldo actual (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	SUM(cantidad) as saldo
            FROM inventario_movimientos
            WHERE
            	id_contenedor = :id_contenedor
            	AND id_activo = :id_activo
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_contenedor", $id_contenedor, PDO::PARAM_INT);
			$statement->bindValue(":id_activo", $id_activo, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			// Si no hay resultados o el saldo es NULL, devolver 0
			return $resultado && isset($resultado['saldo']) ? (int)$resultado['saldo'] : 0;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener saldo actual: " . $e->getMessage());
		}
	}
}
