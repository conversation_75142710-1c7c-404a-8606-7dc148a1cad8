<?php

// Iniciar sesión si es necesario
use App\classes\Inventario;
use App\classes\InventarioMovimiento;
use App\classes\Contenedor;
use App\classes\Activo;
use App\classes\Trabajador;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en prestar_activos.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$contenedores        = [];      // Lista de contenedores
$trabajadores        = [];      // Lista de trabajadores
$activos_disponibles = [];      // Activos disponibles en el contenedor seleccionado
$error_text          = '';
$error_display       = 'none';
$selected_contenedor = null;    // Almacena el ID del contenedor seleccionado
$selected_trabajador = null;    // Almacena el ID del trabajador seleccionado
#endregion init variables

#region region Image Upload Utility Function
/**
 * Handles image upload for inventory movements
 *
 * @param array $file_info File information from $_FILES
 * @param int $movement_id Movement ID for directory structure
 * @param int $asset_id Asset ID for filename
 * @return string|false Returns filename on success, false on failure
 */
function handle_movement_image_upload($file_info, $movement_id, $asset_id): false|string
{
    // Enhanced logging for debugging
    error_log("Image upload attempt - Movement ID: $movement_id, Asset ID: $asset_id");
    error_log("File info: " . print_r($file_info, true));

    // Validate file upload
    if (empty($file_info['tmp_name'])) {
        error_log("No tmp_name provided for image upload");
        return false;
    }

    if ($file_info['error'] !== UPLOAD_ERR_OK) {
        error_log("File upload error code: " . $file_info['error']);
        return false;
    }

    // Define allowed image types
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    $max_file_size = 5 * 1024 * 1024; // 5MB

    // Validate file type
    if (!in_array($file_info['type'], $allowed_types)) {
        error_log("Invalid file type for movement image: " . $file_info['type']);
        return false;
    }

    // Validate file size
    if ($file_info['size'] > $max_file_size) {
        error_log("File too large for movement image: " . $file_info['size'] . " bytes");
        return false;
    }

    // Create upload directory structure
    $base_dir = __ROOT__ . '/resources/uploads/inventario_movimientos/';
    $movement_dir = $base_dir . $movement_id . '/';

    error_log("Creating directory: " . $movement_dir);

    if (!file_exists($movement_dir)) {
        if (!mkdir($movement_dir, 0755, true)) {
            error_log("Failed to create directory: " . $movement_dir);
            return false;
        }
        error_log("Directory created successfully: " . $movement_dir);
    } else {
        error_log("Directory already exists: " . $movement_dir);
    }

    // Generate filename with format: yyyyMMddHHmmss-{asset_id}.{extension}
    $timestamp = date('YmdHis');
    $extension = pathinfo($file_info['name'], PATHINFO_EXTENSION);
    $filename = $timestamp . '-' . $asset_id . '.' . $extension;
    $full_path = $movement_dir . $filename;

    error_log("Attempting to move file to: " . $full_path);

    // Move uploaded file
    if (move_uploaded_file($file_info['tmp_name'], $full_path)) {
        error_log("File uploaded successfully: " . $filename);
        return $filename;
    } else {
        error_log("Failed to move uploaded file from " . $file_info['tmp_name'] . " to: " . $full_path);
        return false;
    }
}
#endregion Image Upload Utility Function

#region region Handle POST Request
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	// IMMEDIATE DEBUG - Log everything being submitted
	error_log("=== POST REQUEST DEBUG ===");
	error_log("POST data: " . print_r($_POST, true));
	error_log("FILES data: " . print_r($_FILES, true));
	error_log("========================");

	// Test directory creation and permissions
	$test_dir = __ROOT__ . '/resources/uploads/inventario_movimientos/test/';
	if (!file_exists($test_dir)) {
		if (mkdir($test_dir, 0755, true)) {
			error_log("SUCCESS: Test directory created at: $test_dir");
			// Try to create a test file
			$test_file = $test_dir . 'test.txt';
			if (file_put_contents($test_file, 'test content')) {
				error_log("SUCCESS: Test file created at: $test_file");
				unlink($test_file); // Clean up
			} else {
				error_log("ERROR: Could not create test file at: $test_file");
			}
		} else {
			error_log("ERROR: Could not create test directory at: $test_dir");
		}
	} else {
		error_log("Test directory already exists: $test_dir");
	}

	// Validar y sanitizar entrada
	$id_contenedor         = filter_input(INPUT_POST, 'id_contenedor', FILTER_VALIDATE_INT);
	$id_trabajador         = filter_input(INPUT_POST, 'id_trabajador', FILTER_VALIDATE_INT);
	$activos_seleccionados = $_POST['activos'] ?? [];
	$confirmacion          = isset($_POST['confirmacion']);
	
	// Almacenar valores seleccionados para persistencia del formulario
	$selected_contenedor = $id_contenedor;
	$selected_trabajador = $id_trabajador;
	
	// Validar campos requeridos en el servidor
	$errors = [];
	
	// Validar contenedor
	if (!$id_contenedor) {
		$errors[] = "Debe seleccionar un contenedor";
	} else {
		// Verificar que el contenedor existe y está activo
		$contenedor = Contenedor::get($id_contenedor, $conexion);
		if (!$contenedor || !$contenedor->isActivo()) {
			$errors[] = "El contenedor seleccionado no es válido";
		}
	}
	
	// Validar activos
	if (empty($activos_seleccionados)) {
		$errors[] = "Debe seleccionar al menos un activo";
	} else {
		// Convertir IDs a enteros para seguridad
		$activos_ids = array_map('intval', $activos_seleccionados);
		
		// Verificar que cada activo existe, pertenece al contenedor y tiene en_contenedor = 1
		if ($id_contenedor) {
			foreach ($activos_ids as $id_activo) {
				$inventario = Inventario::get_by_contenedor_activo($id_contenedor, $id_activo, $conexion);
				if (!$inventario || $inventario->getEn_contenedor() != 1) {
					$errors[] = "Uno o más activos seleccionados no están disponibles en el contenedor";
					break;
				}
			}
		}
	}
	
	// Validar trabajador
	if (!$id_trabajador) {
		$errors[] = "Debe seleccionar un trabajador";
	} else {
		// Verificar que el trabajador existe y está activo
		$trabajador = Trabajador::get($id_trabajador, $conexion);
		if (!$trabajador || !$trabajador->isActivo()) {
			$errors[] = "El trabajador seleccionado no es válido";
		}
	}
	
	// Validar confirmación
	if (!$confirmacion) {
		$errors[] = "Debe aceptar la confirmación de préstamo";
	}
	
	// Si hay errores, mostrarlos
	if (!empty($errors)) {
		$error_display = 'show';
		$error_text    = implode(". ", $errors);
	} else {
		try {
			// Iniciar transacción
			$conexion->beginTransaction();
			
			$activos_prestados = 0;
			$errores           = 0;
			
			foreach ($activos_ids as $id_activo) {
				error_log("=== PROCESSING ASSET ===");
				error_log("Asset ID: $id_activo (type: " . gettype($id_activo) . ")");

				// Obtener el registro de inventario
				$inventario = Inventario::get_by_contenedor_activo($id_contenedor, $id_activo, $conexion);
				
				if ($inventario && $inventario->getEn_contenedor() == 1) {
					// Actualizar el registro de inventario
					$inventario->setId_trabajador($id_trabajador);
					$inventario->setEn_contenedor(0);
					
					// Actualizar en la base de datos usando el método de la clase
					$result = Inventario::prestar_a_trabajador($inventario->getId(), $id_trabajador, $conexion);
					
					if ($result) {
						// Crear registro de movimiento
						$movimiento = new InventarioMovimiento();
						$movimiento->setId_contenedor($id_contenedor);
						$movimiento->setId_activo($id_activo);
						$movimiento->setCantidad(1); // Cantidad fija de 1
						$movimiento->setId_usuario($_SESSION[USR_SESSION]);
						$movimiento->setId_trabajador($id_trabajador); // Asociar con el trabajador seleccionado
						$movimiento->setTipo_movimiento('salida');
						
						$movimiento_result = $movimiento->crear($conexion);

						if ($movimiento_result) {
							error_log("=== MOVEMENT CREATED SUCCESSFULLY ===");
							error_log("Movement ID: $movimiento_result, Asset ID: $id_activo");
							error_log("Checking for image upload...");

							// Debug: Log $_FILES structure
							error_log("DEBUG: _FILES structure: " . print_r($_FILES, true));

							// Check if imagenes_activos exists at all
							if (isset($_FILES['imagenes_activos'])) {
								error_log("imagenes_activos found in _FILES");
								error_log("imagenes_activos structure: " . print_r($_FILES['imagenes_activos'], true));

								// Check what keys are available
								if (isset($_FILES['imagenes_activos']['tmp_name'])) {
									$available_keys = array_keys($_FILES['imagenes_activos']['tmp_name']);
									error_log("Available asset keys in FILES: " . print_r($available_keys, true));
									error_log("Looking for asset key: $id_activo (type: " . gettype($id_activo) . ")");

									// Check if the key exists as string
									$string_key = (string)$id_activo;
									if (isset($_FILES['imagenes_activos']['tmp_name'][$string_key])) {
										error_log("Found asset key as string: $string_key");
									}

									// Check if the key exists as integer
									if (isset($_FILES['imagenes_activos']['tmp_name'][$id_activo])) {
										error_log("Found asset key as integer: $id_activo");
									}
								}
							} else {
								error_log("ERROR: imagenes_activos NOT found in _FILES!");
							}

							// Handle image upload if provided for this asset
							// Try both integer and string keys
							$asset_key = null;
							if (isset($_FILES['imagenes_activos']['tmp_name'][$id_activo]) && !empty($_FILES['imagenes_activos']['tmp_name'][$id_activo])) {
								$asset_key = $id_activo;
							} elseif (isset($_FILES['imagenes_activos']['tmp_name'][(string)$id_activo]) && !empty($_FILES['imagenes_activos']['tmp_name'][(string)$id_activo])) {
								$asset_key = (string)$id_activo;
							}

							if (isset($_FILES['imagenes_activos']) && $asset_key !== null) {

								error_log("SUCCESS: Found image for asset ID: $id_activo");
								error_log("DEBUG: Processing image for asset ID: $id_activo");

								// Prepare file info for the upload function using the correct key
								$file_info = [
									'tmp_name' => $_FILES['imagenes_activos']['tmp_name'][$asset_key],
									'name' => $_FILES['imagenes_activos']['name'][$asset_key],
									'type' => $_FILES['imagenes_activos']['type'][$asset_key],
									'size' => $_FILES['imagenes_activos']['size'][$asset_key],
									'error' => $_FILES['imagenes_activos']['error'][$asset_key]
								];

								// Upload the image
								$filename = handle_movement_image_upload($file_info, $movimiento_result, $id_activo);

								if ($filename) {
									// Update the movement record with the image filename using class method
									try {
										$update_result = InventarioMovimiento::actualizar_imagen($movimiento_result, $filename, $conexion);
										if (!$update_result) {
											error_log("Failed to update movement with image filename for movement ID: $movimiento_result");
										}
									} catch (Exception $e) {
										// Log the error but don't fail the entire transaction
										error_log("Error updating movement with image: " . $e->getMessage());
									}
								} else {
									// Log image upload failure but don't fail the transaction
									error_log("Failed to upload image for asset ID: $id_activo, movement ID: $movimiento_result");
								}
							} else {
								error_log("No image upload found for asset ID: $id_activo");
								if (isset($_FILES['imagenes_activos'])) {
									error_log("imagenes_activos exists but no file for asset $id_activo");
									if (isset($_FILES['imagenes_activos']['tmp_name'])) {
										error_log("Available asset IDs in tmp_name: " . print_r(array_keys($_FILES['imagenes_activos']['tmp_name']), true));
									}
								}
							}

							$activos_prestados++;
						} else {
							$errores++;
						}
					} else {
						$errores++;
					}
				}
			}
			
			// Confirmar transacción
			$conexion->commit();
			
			if ($activos_prestados > 0) {
				// Éxito - redirigir a la lista de inventario con mensaje de éxito
				if ($errores > 0) {
					$_SESSION['flash_message_success'] = "Se prestaron $activos_prestados activos al trabajador, pero hubo $errores errores.";
				} else {
					$_SESSION['flash_message_success'] = "Se prestaron $activos_prestados activos al trabajador correctamente.";
				}
				/*header('Location: linventario');
				exit;*/
			} else {
				$error_display = 'show';
				$error_text    = "No se pudo prestar ningún activo. Por favor, inténtelo de nuevo.";
			}
			
		} catch (Exception $e) {
			// Revertir transacción en caso de error
			if ($conexion->inTransaction()) {
				$conexion->rollBack();
			}
			
			$error_display = 'show';
			$error_text    = "Error: " . $e->getMessage();
		}
	}
	
	// Si llegamos aquí con errores, cargar los activos disponibles para el contenedor seleccionado
	if ($id_contenedor && $error_display == 'show') {
		try {
			// Obtener activos disponibles en el contenedor seleccionado usando el método de la clase
			$inventarios = Inventario::get_list($conexion, $id_contenedor, null, null, 1);
			
			// Convertir objetos Inventario a array asociativo para mantener compatibilidad con el código existente
			$activos_disponibles = [];
			foreach ($inventarios as $inventario) {
				$activo                = [
					'id'                 => $inventario->getId(),
					'id_activo'          => $inventario->getId_activo(),
					'activo_descripcion' => $inventario->getActivo_descripcion() ?? '',
					'marca'              => $inventario->getActivo_marca() ?? '',
					'modelo'             => $inventario->getActivo_modelo() ?? ''
				];
				$activos_disponibles[] = $activo;
			}
		} catch (Exception $e) {
			error_log("Error al cargar activos disponibles: " . $e->getMessage());
		}
	}
}
#endregion Handle POST Request

#region region AJAX Request for Activos
// Si es una solicitud AJAX para obtener activos de un contenedor
if (isset($_GET['ajax']) && $_GET['ajax'] == 'get_activos' && isset($_GET['id_contenedor'])) {
	$id_contenedor = filter_input(INPUT_GET, 'id_contenedor', FILTER_VALIDATE_INT);
	
	if ($id_contenedor) {
		try {
			// Obtener activos disponibles en el contenedor seleccionado usando el método de la clase
			$inventarios = Inventario::get_list($conexion, $id_contenedor, null, null, 1);
			
			// Convertir objetos Inventario a array asociativo para mantener compatibilidad con el código existente
			$activos = [];
			foreach ($inventarios as $inventario) {
				$activo    = [
					'id'                 => $inventario->getId(),
					'id_activo'          => $inventario->getId_activo(),
					'activo_descripcion' => $inventario->getActivo_descripcion() ?? '',
					'marca'              => $inventario->getActivo_marca() ?? '',
					'modelo'             => $inventario->getActivo_modelo() ?? ''
				];
				$activos[] = $activo;
			}
			
			// Devolver JSON
			header('Content-Type: application/json');
			echo json_encode($activos);
			exit;
		} catch (Exception $e) {
			// Devolver error en formato JSON
			header('Content-Type: application/json');
			echo json_encode(['error' => $e->getMessage()]);
			exit;
		}
	} else {
		// Devolver error en formato JSON
		header('Content-Type: application/json');
		echo json_encode(['error' => 'ID de contenedor no válido']);
		exit;
	}
}
#endregion AJAX Request for Activos

#region try
try {
	// Obtener lista de contenedores para el dropdown
	$contenedores = Contenedor::get_list($conexion);
	
	// Obtener lista de trabajadores para el dropdown
	$trabajadores = Trabajador::get_list($conexion);
	
	// Si hay un contenedor seleccionado, cargar sus activos disponibles
	if ($selected_contenedor) {
		// Obtener activos disponibles en el contenedor seleccionado usando el método de la clase
		$inventarios = Inventario::get_list($conexion, $selected_contenedor, null, null, 1);
		
		// Convertir objetos Inventario a array asociativo para mantener compatibilidad con el código existente
		$activos_disponibles = [];
		foreach ($inventarios as $inventario) {
			$activo                = [
				'id'                 => $inventario->getId(),
				'id_activo'          => $inventario->getId_activo(),
				'activo_descripcion' => $inventario->getActivo_descripcion() ?? '',
				'marca'              => $inventario->getActivo_marca() ?? '',
				'modelo'             => $inventario->getActivo_modelo() ?? ''
			];
			$activos_disponibles[] = $activo;
		}
	}
	
} catch (PDOException $e) {
	// Manejo específico para errores de base de datos
	error_log("Database error fetching data: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de contenedores o trabajadores.";
} catch (Exception $e) {
	// Manejo general de errores
	error_log("Error fetching data: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/prestar_activos.view.php';

?>
