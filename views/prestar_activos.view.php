<?php
#region region DOCS

/** @var Contenedor[] $contenedores */
/** @var Trabajador[] $trabajadores */
/** @var array $activos_disponibles */
/** @var string $error_text */
/** @var string $error_display */
/** @var int|null $selected_contenedor */
/** @var int|null $selected_trabajador */

use App\classes\Contenedor;
use App\classes\Trabajador;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Prestar Activos</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
	
	<style>
        /* Custom styles for validation */
        .was-validated #activos-validation:invalid ~ #activos-card {
            border-color: #dc3545;
        }

        .was-validated #activos-validation:valid ~ #activos-card {
            border-color: #198754;
        }

        /* Custom styles for image association section */
        .activo-seleccionado-item {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 1rem;
        }

        .activo-seleccionado-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        #imagen-asociacion-section .card {
            border-color: #0d6efd;
        }

        #imagen-asociacion-section .card-header {
            background-color: rgba(13, 110, 253, 0.1);
            border-bottom-color: #0d6efd;
        }
	</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Prestar Activos a Trabajador</h4>
				<p class="mb-0 text-muted">Seleccione los activos que desea prestar a un trabajador</p>
			</div>
			<div class="ms-auto">
				<a href="linventario" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver al listado</a>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region ALERT ERROR ?>
		<?php if ($error_display === 'show'): ?>
			<div class="alert alert-danger alert-dismissible fade show">
				<strong>Error!</strong> <?php echo $error_text; ?>
				<button type="button" class="btn-close" data-bs-dismiss="alert"></button>
			</div>
		<?php endif; ?>
		<?php #endregion ALERT ERROR ?>
		
		<?php #region region FORM PANEL ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">Formulario de Préstamo de Activos</h4>
			</div>
			<div class="panel-body">
				<form action="prestar_activos" method="POST" id="prestar-form" enctype="multipart/form-data">
					<div class="alert alert-info">
						Seleccione el contenedor, los activos disponibles y el trabajador al que se prestarán.
					</div>
					
					<!-- Contenedor Selection -->
					<div class="row">
						<div class="col-md-12">
							<div class="mb-3">
								<label for="id_contenedor" class="form-label">Contenedor: <span class="text-danger">*</span></label>
								<select class="form-select" id="id_contenedor" name="id_contenedor" required>
									<option value="">-- Seleccione un contenedor --</option>
									<?php foreach ($contenedores as $contenedor): ?>
										<option value="<?php echo $contenedor->getId(); ?>" <?php echo ($selected_contenedor == $contenedor->getId()) ? 'selected' : ''; ?>>
											<?php echo htmlspecialchars($contenedor->getDescripcion()); ?>
										</option>
									<?php endforeach; ?>
								</select>
								<div class="invalid-feedback">Por favor seleccione un contenedor.</div>
							</div>
						</div>
					</div>
					
					<!-- Activos Selection -->
					<div class="row">
						<div class="col-md-12">
							<div class="mb-3">
								<!-- Hidden input for activos validation -->
								<input type="hidden" id="activos-validation" name="activos-validation" required>
								
								<div class="card" id="activos-card">
									<div class="card-header d-flex align-items-center">
										<h5 class="mb-0">Activos disponibles en el contenedor <span class="text-danger">*</span></h5>
										<div class="ms-auto">
											<div class="form-check">
												<input class="form-check-input" type="checkbox" id="select-all">
												<label class="form-check-label" for="select-all">
													Seleccionar todos
												</label>
											</div>
										</div>
									</div>
									<div class="card-body" style="max-height: 300px; overflow-y: auto;">
										<div id="activos-loading" class="text-center py-3" style="display: none;">
											<div class="spinner-border text-primary" role="status">
												<span class="visually-hidden">Cargando...</span>
											</div>
											<p class="mt-2">Cargando activos disponibles...</p>
										</div>
										
										<div id="activos-empty" class="alert alert-warning <?php echo (empty($activos_disponibles) && $selected_contenedor) ? '' : 'd-none'; ?>">
											No hay activos disponibles en este contenedor.
										</div>
										
										<div id="activos-select-container" class="<?php echo (empty($activos_disponibles) && !$selected_contenedor) ? 'd-none' : ''; ?>">
											<input type="text" class="form-control form-control-sm mb-3" id="search-activos" placeholder="Buscar activos...">
											
											<div class="row" id="activos-container">
												<?php if (!empty($activos_disponibles)): ?>
													<?php foreach ($activos_disponibles as $activo): ?>
														<div class="col-md-4 mb-2 activo-item">
															<div class="form-check">
																<input class="form-check-input activo-checkbox" type="checkbox" name="activos[]" value="<?php echo $activo['id_activo']; ?>" id="activo-<?php echo $activo['id_activo']; ?>">
																<label class="form-check-label" for="activo-<?php echo $activo['id_activo']; ?>">
																	<?php echo htmlspecialchars($activo['activo_descripcion']); ?>
																	<?php if (!empty($activo['marca']) || !empty($activo['modelo'])): ?>
																		<small class="d-block text-muted">
																			<?php echo htmlspecialchars(trim(($activo['marca'] ?? '') . ' ' . ($activo['modelo'] ?? ''))); ?>
																		</small>
																	<?php endif; ?>
																</label>
															</div>
														</div>
													<?php endforeach; ?>
												<?php endif; ?>
											</div>
										</div>
									</div>
								</div>
								<div class="invalid-feedback" id="activos-feedback" style="display: none;">Por favor seleccione al menos un activo para prestar.</div>
							</div>
						</div>
					</div>

					<!-- Image Association Section -->
					<div class="row" id="imagen-asociacion-section" style="display: none;">
						<div class="col-md-12">
							<div class="mb-3">
								<div class="card">
									<div class="card-header">
										<h5 class="mb-0">Asociar Imágenes a Activos Seleccionados</h5>
										<small class="text-muted">Opcional: Puede asociar imágenes a los activos seleccionados</small>
									</div>
									<div class="card-body">
										<div id="activos-seleccionados-container">
											<!-- Los activos seleccionados se mostrarán aquí dinámicamente -->
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Trabajador Selection -->
					<div class="row">
						<div class="col-md-12">
							<div class="mb-3">
								<label for="id_trabajador" class="form-label">Trabajador: <span class="text-danger">*</span></label>
								<select class="form-select" id="id_trabajador" name="id_trabajador" required>
									<option value="">-- Seleccione un trabajador --</option>
									<?php foreach ($trabajadores as $trabajador): ?>
										<option value="<?php echo $trabajador->getId(); ?>" <?php echo ($selected_trabajador == $trabajador->getId()) ? 'selected' : ''; ?>>
											<?php echo $trabajador->getCedula() . ' - ' . htmlspecialchars($trabajador->getNombreCompleto()); ?>
										</option>
									<?php endforeach; ?>
								</select>
								<div class="invalid-feedback">Por favor seleccione un trabajador.</div>
							</div>
						</div>
					</div>
					
					<!-- Confirmation Checkbox -->
					<div class="row">
						<div class="col-md-12">
							<div class="mb-3">
								<div class="form-check">
									<input class="form-check-input" type="checkbox" id="confirmacion" name="confirmacion" required>
									<label class="form-check-label" for="confirmacion">
										Acepto que estoy recibiendo estos activos para trabajos en el parque <span class="text-danger">*</span>
									</label>
									<div class="invalid-feedback">Debe aceptar la confirmación para continuar.</div>
								</div>
							</div>
						</div>
					</div>
					
					<!-- Submit Buttons -->
					<div class="row mt-3">
						<div class="col-md-12">
							<button type="submit" class="btn btn-primary" id="btn-prestar">
								<i class="fa fa-share fa-fw me-1"></i> Prestar
							</button>
							<a href="linventario" class="btn btn-secondary ms-2">
								<i class="fa fa-times fa-fw me-1"></i> Cancelar
							</a>
						</div>
					</div>
				</form>
			</div>
		</div>
		<?php #endregion FORM PANEL ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script src="<?php echo RUTA_RESOURCES ?>js/prestar_activos.js"></script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
