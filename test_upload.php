<?php
// Test file upload configuration
echo "<h2>PHP Upload Configuration Test</h2>";

echo "<h3>Upload Settings:</h3>";
echo "file_uploads: " . (ini_get('file_uploads') ? 'Enabled' : 'Disabled') . "<br>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "post_max_size: " . ini_get('post_max_size') . "<br>";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "<br>";
echo "memory_limit: " . ini_get('memory_limit') . "<br>";

echo "<h3>Directory Test:</h3>";
$test_dir = __DIR__ . '/resources/uploads/inventario_movimientos/';
echo "Test directory: $test_dir<br>";
echo "Directory exists: " . (file_exists($test_dir) ? 'Yes' : 'No') . "<br>";
echo "Directory writable: " . (is_writable($test_dir) ? 'Yes' : 'No') . "<br>";

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h3>POST Data:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    echo "<h3>FILES Data:</h3>";
    echo "<pre>" . print_r($_FILES, true) . "</pre>";
    
    if (isset($_FILES['test_image']) && $_FILES['test_image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = $test_dir . 'test/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $filename = 'test_' . date('YmdHis') . '.jpg';
        $full_path = $upload_dir . $filename;
        
        if (move_uploaded_file($_FILES['test_image']['tmp_name'], $full_path)) {
            echo "<p style='color: green;'>SUCCESS: File uploaded to $full_path</p>";
        } else {
            echo "<p style='color: red;'>ERROR: Failed to move uploaded file</p>";
        }
    } else {
        echo "<p style='color: red;'>ERROR: No file uploaded or upload error</p>";
        if (isset($_FILES['test_image'])) {
            echo "Upload error code: " . $_FILES['test_image']['error'];
        }
    }
}
?>

<form method="POST" enctype="multipart/form-data">
    <h3>Test Upload:</h3>
    <input type="file" name="test_image" accept="image/*" required>
    <button type="submit">Upload Test Image</button>
</form>
